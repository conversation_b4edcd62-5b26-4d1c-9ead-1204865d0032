<template>
  <PanelBox title="应急专家">
    <div class="panel-content">
      <!-- 使用滚动表格组件 -->
      <ScrollTable 
        :columns="tableColumns" 
        :data="expertList" 
        :autoScroll="true" 
        :scrollSpeed="3000"
        :tableHeight="tableHeight" 
        :visibleRows="5" 
        @row-click="openExpertDetail">
        <!-- 自定义序号列样式 -->
        <template #index="{ index }">
          <span>{{ index + 1 }}</span>
        </template>
        <!-- 自定义擅长领域列样式 -->
        <template #expertArea="{ row }">
          <span>{{ row.expertArea }}</span>
        </template>
      </ScrollTable>
    </div>
    
    <!-- 专家详情弹窗 -->
    <ExpertDetailModal 
      v-if="showExpertDetail" 
      :expertId="selectedExpertId" 
      @close="closeExpertDetail" 
    />
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import ExpertDetailModal from './ExpertDetailModal.vue'
import { getEmergencyExpertList } from '@/api/comprehensive.js'

// 表格列配置
const tableColumns = ref([
  { title: '序号', dataIndex: 'index', width: '12%', fontSize: '13px', slot: 'index' },
  { title: '专家姓名', dataIndex: 'name', width: '30%', fontSize: '13px' },
  { title: '擅长领域', dataIndex: 'expertArea', width: '30%', fontSize: '13px', slot: 'expertArea' },
  { title: '详情', dataIndex: 'detail', width: '30%', fontSize: '13px' }
])

// 应急专家数据
const expertList = ref([])

// 弹窗相关状态
const showExpertDetail = ref(false)
const selectedExpertId = ref('')

// 动态计算表格高度
const tableHeight = computed(() => {
  // 根据不同分辨率动态调整表格高度
  if (window.innerHeight === 910) {
    return '220px' // 910px高度的屏幕使用更小的表格高度
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '180px' // 940px-1055px高度的屏幕
  } else if (window.innerWidth >= 2561) {
    return '220px' // 超宽屏幕
  } else if (window.innerWidth >= 1920 && window.innerWidth <= 2560) {
    return '220px' // 标准宽屏
  } else {
    return '180px' // 默认高度
  }
})

// 打开专家详情
const openExpertDetail = (row) => {
  console.log('查看专家详情:', row)
  selectedExpertId.value = row.id
  showExpertDetail.value = true
}

// 关闭专家详情弹窗
const closeExpertDetail = () => {
  showExpertDetail.value = false
  selectedExpertId.value = ''
}

// 获取应急专家数据
const fetchExpertData = async () => {
  try {
    console.log('开始获取应急专家数据')
    
    const response = await getEmergencyExpertList()
    
    if (response.code === 200 && response.data) {
      // 处理数据格式，适配表格显示
      expertList.value = response.data.map((item, index) => ({
        id: item.id,
        name: item.expertName || '-',
        expertArea: item.professionalField || '-',
        detail: '查看',
        // 保留原始数据
        originalData: item
      }))
      
      console.log('应急专家数据获取成功:', expertList.value.length, '条')
    } else {
      console.error('获取应急专家数据失败:', response.message)
      // 使用默认数据作为备用
      expertList.value = [
        { id: '1', name: '周俊义', expertArea: '燃气安全', detail: '查看' },
        { id: '2', name: '张志刚', expertArea: '突发事件', detail: '查看' },
        { id: '3', name: '陈晓洲', expertArea: '交通安全', detail: '查看' },
        { id: '4', name: '肖红', expertArea: '桥梁结构', detail: '查看' },
        { id: '5', name: '张道平', expertArea: '突发事件', detail: '查看' }
      ]
    }
  } catch (error) {
    console.error('获取应急专家数据异常:', error)
    // 使用默认数据作为备用
    expertList.value = [
      { id: '1', name: '周俊义', expertArea: '燃气安全', detail: '查看' },
      { id: '2', name: '张志刚', expertArea: '突发事件', detail: '查看' },
      { id: '3', name: '陈晓洲', expertArea: '交通安全', detail: '查看' },
      { id: '4', name: '肖红', expertArea: '桥梁结构', detail: '查看' },
      { id: '5', name: '张道平', expertArea: '突发事件', detail: '查看' }
    ]
  }
}

onMounted(() => {
  // 初始化时获取数据
  fetchExpertData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table td) {
  cursor: pointer;
  transition: background-color 0.2s;
  line-height: 2.2rem;
  text-align: center; /* 居中显示内容 */
}

:deep(.scroll-table th) {
  text-align: center; /* 表头居中 */
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
    gap: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
    gap: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
    gap: 18px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
    gap: 15px;
  }
}

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
}
</style>