<template>
  <PanelBox title="桥梁基本信息">
    <div class="panel-content">
      <div class="info-container">
        <!-- 左侧基本信息 -->
        <div class="info-left">
          <div class="info-item">
            <span class="info-label">桥梁名称：</span>
            <span class="info-value bridge-name">{{ bridgeInfo.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">桥梁类型：</span>
            <span class="info-value">{{ bridgeInfo.type }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">养护等级：</span>
            <span class="info-value">{{ bridgeInfo.maintenanceLevel }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">安全评估得分：</span>
            <span class="info-value safety-score">{{ bridgeInfo.safetyScore }}分</span>
          </div>
          <div class="info-item">
            <span class="info-label">风险等级：</span>
            <span class="info-value risk-level">{{ bridgeInfo.riskLevel }}</span>
          </div>
        </div>
        
        <!-- 右侧图片区域 -->
        <div class="info-right">
          <div class="bridge-image">
            <div class="image-placeholder">
              <div class="placeholder-icon">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                  <path fill="#A0A0A0" d="M32 28L24 20L16 28H8L20 16L32 28Z"/>
                  <circle cx="28" cy="12" r="4" fill="#A0A0A0"/>
                  <rect x="4" y="4" width="32" height="32" rx="2" stroke="#A0A0A0" stroke-width="2" fill="none"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'

// 定义props
const props = defineProps({
  bridgeId: {
    type: [String, Number],
    required: true
  },
  bridgeName: {
    type: String,
    default: '未知桥梁'
  }
})

// 桥梁基本信息数据
const bridgeInfo = ref({
  name: props.bridgeName,
  type: '梁式桥',
  maintenanceLevel: '一类养护',
  safetyScore: 93,
  riskLevel: '重大风险'
})

</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.info-container {
  flex: 1;
  display: flex;
  gap: 20px;
  align-items: stretch;
}

.info-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding-right: 10px;
  margin-left: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  min-width: 100px;
  flex-shrink: 0;
}

.info-value {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
}

/* 特殊样式 */
.bridge-name {
  color: #3CF3FF;
  font-weight: 600;
}

.safety-score {
  color: #3CF3FF;
  font-weight: 600;
  font-family: D-DIN, D-DIN;
  font-size: 16px;
}

.risk-level {
  color: #FF6B6B;
  font-weight: 600;
}

.info-right {
  width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 20px;
}

.bridge-image {
  width: 100%;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-icon svg {
  opacity: 0.6;
}

/* 响应式布局适配 */
/* @media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
  
  .info-label {
    font-size: 14px;
    min-width: 100px;
  }
  
  .info-value {
    font-size: 14px;
  }
  
  .safety-score {
    font-size: 16px;
  }
} */

/* @media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .info-container {
    gap: 15px;
  }
  
  .info-label {
    font-size: 13px;
    min-width: 90px;
  }
  
  .info-value {
    font-size: 13px;
  }
  
  .safety-score {
    font-size: 15px;
  }
  
  .info-right {
    width: 140px;
  }
  
  .bridge-image {
    height: 100px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
  
  .info-label {
    font-size: 15px;
    min-width: 110px;
  }
  
  .info-value {
    font-size: 15px;
  }
  
  .safety-score {
    font-size: 18px;
  }
  
  .info-right {
    width: 180px;
  }
  
  .bridge-image {
    height: 140px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
  }
  
  .info-item {
    margin-bottom: 8px;
  }
  
  .info-label {
    font-size: 12px;
    min-width: 85px;
  }
  
  .info-value {
    font-size: 12px;
  }
  
  .safety-score {
    font-size: 14px;
  }
  
  .info-right {
    width: 120px;
  }
  
  .bridge-image {
    height: 80px;
  }
  
  .placeholder-icon svg {
    width: 30px;
    height: 30px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
  }
  
  .info-container {
    gap: 10px;
  }
  
  .info-item {
    margin-bottom: 6px;
  }
  
  .info-label {
    font-size: 11px;
    min-width: 80px;
  }
  
  .info-value {
    font-size: 11px;
  }
  
  .safety-score {
    font-size: 13px;
  }
  
  .info-right {
    width: 100px;
  }
  
  .bridge-image {
    height: 70px;
  }
  
  .placeholder-icon svg {
    width: 25px;
    height: 25px;
  }
} */
</style> 