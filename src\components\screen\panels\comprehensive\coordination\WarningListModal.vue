<template>
  <teleport to="body">
    <transition name="fade">
      <div class="modal-overlay" @click.self="closeModal">
        <div class="modal-container">
          <div class="modal-header">
            <div class="modal-title">预警列表</div>
            <div class="close-icon" @click="closeModal">×</div>
          </div>
          
          <!-- 查询条件区域 -->
          <div class="modal-filters">
            <div class="filter-row">
              <div class="filter-item">
                <label>预警编号:</label>
                <input v-model="filters.warningCode" type="text" placeholder="请输入报警编号" class="filter-input" />
              </div>
              <div class="filter-item">
                <label>所属专项:</label>
                <select v-model="filters.relatedBusiness" class="filter-select">
                  <option value="">全部</option>
                  <option :value="7000501">燃气</option>
                  <option :value="7000502">排水</option>
                  <option :value="7000503">供热</option>
                  <option :value="7000504">桥梁</option>
                </select>
              </div>
              <div class="filter-item">
                <label>预警等级:</label>
                <select v-model="filters.warningLevel" class="filter-select">
                   <option value="">全部</option>
                  <option :value="7002201">一级预警</option>
                  <option :value="7002202">二级预警</option>
                  <option :value="7002203">三级预警</option>
                </select>
              </div>
            </div>
            <div class="filter-row">
              <div class="filter-item">
                <label>预警状态:</label>
                <select v-model="filters.warningStatus" class="filter-select">
                  <option value="">全部</option>
                  <option :value="7002301">待处置</option>
                  <option :value="7002302">处置中</option>
                  <option :value="7002303">已处置</option>
                  <option :value="7002304">已解除</option>
                </select>
              </div>
              <div class="filter-item">
                <label>预警时间:</label>
                <div class="date-range">
                  <input v-model="filters.startTime" type="date" class="filter-input date-input" />
                  <span class="date-separator">至</span>
                  <input v-model="filters.endTime" type="date" class="filter-input date-input" />
                </div>
              </div>
            </div>
            <div class="filter-actions">
              <button @click="handleSearch" class="search-btn">查询</button>
              <button @click="handleReset" class="reset-btn">重置</button>
            </div>
          </div>

          <!-- 数据表格区域 -->
          <div class="modal-content">
            <!-- Loading 效果 -->
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner"></div>
              <div class="loading-text">数据加载中...</div>
            </div>
            
            <!-- 数据表格 -->
            <div v-else class="table-container">
              <table class="warning-table">
                <thead>
                  <tr>
                    <th><input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected" /></th>
                    <th>序号</th>
                    <th>预警类型</th>
                    <th>预警编号</th>
                    <th>预警等级</th>
                    <th>所属专项</th>
                    <th>预警时间</th>
                    <th>定位</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in warningList" :key="item.id" class="table-row">
                    <td>
                      <input 
                        type="checkbox" 
                        :checked="selectedItems.includes(item.id)"
                        @change="toggleSelectItem(item.id)"
                      />
                    </td>
                    <td>{{ (pagination.current - 1) * pagination.pageSize + index + 1 }}</td>
                    <td>{{ item.warningTypeName || '-' }}</td>
                    <td>{{ item.warningCode || '-' }}</td>
                    <td>
                      <span :class="getLevelClass(item.warningLevelName)">{{ item.warningLevelName || '-' }}</span>
                    </td>
                    <td>{{ item.relatedBusinessName || '-' }}</td>
                    <td>{{ formatTime(item.warningTime || item.publishTime) }}</td>
                    <td>
                      <div class="location-icon" @click="locateWarning(item)"></div>
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <!-- 无数据状态 -->
              <div v-if="!loading && warningList.length === 0" class="no-data">
                <NoData />
              </div>
            </div>
            
            <!-- 分页组件 -->
            <div v-if="warningList.length > 0" class="pagination-container">
              <div class="pagination-info">
                共 {{ pagination.total }} 条数据
              </div>
              <div class="pagination-controls">
                <button 
                  @click="changePage(pagination.current - 1)" 
                  :disabled="pagination.current <= 1"
                  class="page-btn"
                >
                  上一页
                </button>
                <span class="page-info">
                  {{ pagination.current }} / {{ totalPages }}
                </span>
                <button 
                  @click="changePage(pagination.current + 1)" 
                  :disabled="pagination.current >= totalPages"
                  class="page-btn"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import moment from 'moment'
import { getWarningInfoPageScreen } from '@/api/comprehensive.js'
import NoData from '@/components/common/NoData.vue'
const emit = defineEmits(['close'])

// 筛选条件
const filters = ref({
  warningCode: '',
  relatedBusiness: '',
  warningLevel: '',
  warningStatus: '',
  startTime: '',
  endTime: ''
})

// 加载状态
const loading = ref(false)

// 分页信息
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 预警数据列表
const warningList = ref([])

// 选中的项目
const selectedItems = ref([])

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(pagination.value.total / pagination.value.pageSize)
})

// 计算是否全选
const isAllSelected = computed(() => {
  return warningList.value.length > 0 && selectedItems.value.length === warningList.value.length
})

/**
 * 关闭弹窗
 */
const closeModal = () => {
  emit('close')
}

/**
 * 获取预警信息列表
 * @param {boolean} isSearch 是否为搜索操作
 */
const fetchWarningList = async (isSearch = false) => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params = {
      warningLevel: filters.value.warningLevel,
      warningStatus: filters.value.warningStatus, // 弹窗中不筛选状态
      relatedBusiness: filters.value.relatedBusiness,
      warningCode: filters.value.warningCode
    }
    
    // 如果有时间筛选
    if (filters.value.startTime && filters.value.endTime) {
      params.startTime = moment(filters.value.startTime).format('YYYY-MM-DD 00:00:00')
      params.endTime = moment(filters.value.endTime).format('YYYY-MM-DD 23:59:59')
    } else {
      // 默认查询近一月数据
      params.endTime = moment().format('YYYY-MM-DD HH:mm:ss')
      params.startTime = moment().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss')
    }
    
    const response = await getWarningInfoPageScreen(
      pagination.value.current,
      pagination.value.pageSize,
      params
    )
    
    if (response.code === 200 && response.data) {
      const { records, total } = response.data
      warningList.value = processWarningData(records || [])
      pagination.value.total = total || 0
      
      console.log('预警列表弹窗数据获取成功:', warningList.value.length, '条')
    }
  } catch (error) {
    console.error('获取预警列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理预警数据格式
 * @param {Array} records API返回的原始数据
 * @returns {Array} 处理后的数据
 */
const processWarningData = (records) => {
  return records.map(item => ({
    id: item.id,
    warningCode: item.warningCode || '',
    warningTypeName: item.warningTypeName || '',
    warningLevelName: item.warningLevelName || '',
    relatedBusinessName: item.relatedBusinessName || '',
    warningTime: item.warningTime || item.publishTime,
    warningLocation: item.warningLocation,
    // 保留原始数据
    originalData: item
  }))
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.value.current = 1
  selectedItems.value = []
  fetchWarningList(true)
}

/**
 * 重置筛选条件
 */
const handleReset = () => {
  filters.value = {
    warningCode: '',
    warningStatus: '',
    warningLevel: '',
    relatedBusiness: '',
    startTime: '',
    endTime: ''
  }
  pagination.value.current = 1
  selectedItems.value = []
  fetchWarningList()
}

/**
 * 切换页码
 * @param {number} page 目标页码
 */
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    pagination.value.current = page
    fetchWarningList()
  }
}

/**
 * 切换全选状态
 */
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedItems.value = []
  } else {
    selectedItems.value = warningList.value.map(item => item.id)
  }
}

/**
 * 切换单项选择状态
 * @param {string} itemId 项目ID
 */
const toggleSelectItem = (itemId) => {
  const index = selectedItems.value.indexOf(itemId)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(itemId)
  }
}

/**
 * 获取预警级别样式类
 * @param {string} level 预警级别
 * @returns {string} 样式类名
 */
const getLevelClass = (level) => {
  const classMap = {
    '一级': 'level-one',
    '二级': 'level-two', 
    '三级': 'level-three'
  }
  return classMap[level] || 'level-three'
}

/**
 * 格式化时间
 * @param {string} time 时间字符串
 * @returns {string} 格式化后的时间
 */
const formatTime = (time) => {
  if (!time) return '2023-09-07 03:52:17'
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 定位预警
 * @param {object} item 预警项目
 */
const locateWarning = (item) => {
  console.log('定位预警:', item)
  // 这里可以添加地图定位逻辑
}

onMounted(() => {
  // 重置状态
  pagination.value.current = 1
  selectedItems.value = []
  
  // 设置默认时间范围（近一月）
  filters.value.endTime = moment().format('YYYY-MM-DD')
  filters.value.startTime = moment().subtract(1, 'month').format('YYYY-MM-DD')
  
  // 获取初始数据
  fetchWarningList()
})
</script>

<style scoped>
/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  width: 900px;
  max-height: 80vh;
  background: linear-gradient(180deg, rgba(0, 22, 72, 0.9) 0%, rgba(0, 35, 91, 0.9) 100%);
  border: 1px solid rgba(59, 141, 242, 0.5);
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.modal-title {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
}

.close-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.close-icon:hover {
  color: #FFFFFF;
}

/* 筛选条件样式 */
.modal-filters {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  align-items: center;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: #FFFFFF;
  white-space: nowrap;
}

.filter-input,
.filter-select {
  background-color: transparent;
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 4px;
  color: #FFFFFF;
  padding: 5px 10px;
  font-size: 14px;
  outline: none;
  min-width: 120px;
}

.filter-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.filter-select option {
  background-color: #003366;
  color: #FFFFFF;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-input {
  min-width: 140px;
}

.date-separator {
  color: #FFFFFF;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.search-btn,
.reset-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.search-btn {
  background-color: #3AA1FF;
  color: #FFFFFF;
}

.search-btn:hover {
  background-color: #66B8FF;
}

.reset-btn {
  background-color: transparent;
  color: #FFFFFF;
  border: 1px solid rgba(59, 141, 242, 0.5);
}

.reset-btn:hover {
  background-color: rgba(59, 141, 242, 0.2);
}

/* 内容区域样式 */
.modal-content {
  flex: 1;
  padding: 15px 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Loading 效果样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #3AA1FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-text {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: auto;
}

.warning-table {
  width: 100%;
  border-collapse: collapse;
  font-family: PingFangSC, 'PingFang SC';
}

.warning-table th,
.warning-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid rgba(59, 141, 242, 0.3);
}

.warning-table th {
  background-color: rgba(59, 141, 242, 0.2);
  color: #FFFFFF;
  font-weight: 500;
  font-size: 14px;
  position: sticky;
  top: 0;
  z-index: 1;
}

.warning-table td {
  color: #FFFFFF;
  font-size: 14px;
}

.table-row:hover {
  background-color: rgba(59, 141, 242, 0.1);
}

/* 预警级别样式 */
.level-one {
  color: #FF3900;
  font-weight: 500;
}

.level-two {
  color: #FF6817;
  font-weight: 500;
}

.level-three {
  color: #FFD32E;
  font-weight: 500;
}

/* 定位图标 */
.location-icon {
  width: 20px;
  height: 20px;
  background: url('@/assets/images/screen/common/location.svg') no-repeat center/cover;
  cursor: pointer;
  margin: 0 auto;
}

.location-icon:hover {
  opacity: 0.7;
}

/* 无数据状态 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.6);
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.no-data-text {
  font-size: 14px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid rgba(59, 141, 242, 0.3);
  margin-top: 15px;
  flex-shrink: 0;
}

.pagination-info {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.page-btn {
  padding: 6px 12px;
  background-color: transparent;
  border: 1px solid rgba(59, 141, 242, 0.5);
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.page-btn:hover:not(:disabled) {
  background-color: rgba(59, 141, 242, 0.2);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-family: PingFangSC, 'PingFang SC';
  font-size: 14px;
  color: #FFFFFF;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式适配 */
@media screen and (max-width: 1200px) {
  .modal-container {
    width: 90%;
    max-width: 800px;
  }
  
  .filter-row {
    flex-wrap: wrap;
  }
}
</style>