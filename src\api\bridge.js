import request from '@/utils/request'

// 获取桥梁设备信息分页列表
export function getPipelineInfoPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmMonitorDevice/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁设备列表
export function getPipelineInfoList(params = {}) {
  return request({
    url: '/bridge/usmMonitorDevice/list',
    method: 'post',
    data: params
  })
}
// 获取桥梁设备指标列表
export function getMonitorIndicatorsList(data) {
  return request({
    url: '/bridge/usmMonitorIndicators/list',
    method: 'post',
    data
  })
}

// 获取桥梁设备信息详情
export function getPipelineInfoDetail(id) {
  return request({
    url: `/bridge/usmMonitorDevice/${id}`,
    method: 'get'
  })
}

// 新增桥梁设备信息
export function savePipelineInfo(data) {
  return request({
    url: '/bridge/usmMonitorDevice/save',
    method: 'post',
    data
  })
}

// 更新桥梁设备信息
export function updatePipelineInfo(data) {
  return request({
    url: '/bridge/usmMonitorDevice/update',
    method: 'post',
    data
  })
}

// 删除桥梁设备信息
export function deletePipelineInfo(id) {
  return request({
    url: `/bridge/usmMonitorDevice/${id}`,
    method: 'delete'
  })
}

// 获取桥梁基础信息列表（用于对象选择下拉框）
export function getBridgeBasicInfoList(params = {}) {
  return request({
    url: '/bridge/usmBridgeBasicInfo/list',
    method: 'post',
    data: params
  })
}

// ==================== 桥梁基础信息管理 API ====================

// 获取桥梁基础信息分页列表
export function getBridgeBasicInfoPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmBridgeBasicInfo/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁基础信息详情
export function getBridgeBasicInfoDetail(id) {
  return request({
    url: `/bridge/usmBridgeBasicInfo/${id}`,
    method: 'get'
  })
}

// 新增桥梁基础信息
export function saveBridgeBasicInfo(data) {
  return request({
    url: '/bridge/usmBridgeBasicInfo/save',
    method: 'post',
    data
  })
}

// 更新桥梁基础信息
export function updateBridgeBasicInfo(data) {
  return request({
    url: '/bridge/usmBridgeBasicInfo/update',
    method: 'post',
    data
  })
}

// 删除桥梁基础信息
export function deleteBridgeBasicInfo(id) {
  return request({
    url: `/bridge/usmBridgeBasicInfo/${id}`,
    method: 'delete'
  })
}

// ==================== 一般资料 API ====================

// 获取一般资料详情
export function getBridgeProfileGeneralDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileGeneral/${bridgeId}`,
    method: 'get'
  })
}

// 新增一般资料
export function saveBridgeProfileGeneral(data) {
  return request({
    url: '/bridge/usmBridgeProfileGeneral/save',
    method: 'post',
    data
  })
}

// 更新一般资料
export function updateBridgeProfileGeneral(data) {
  return request({
    url: '/bridge/usmBridgeProfileGeneral/update',
    method: 'post',
    data
  })
}

// 删除一般资料
export function deleteBridgeProfileGeneral(id) {
  return request({
    url: `/bridge/usmBridgeProfileGeneral/${id}`,
    method: 'delete'
  })
}

// ==================== 上部结构 API ====================

// 获取上部结构详情
export function getBridgeProfileSuperstructureDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileSuperstructure/${bridgeId}`,
    method: 'get'
  })
}

// 新增上部结构
export function saveBridgeProfileSuperstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSuperstructure/save',
    method: 'post',
    data
  })
}

// 更新上部结构
export function updateBridgeProfileSuperstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSuperstructure/update',
    method: 'post',
    data
  })
}

// 删除上部结构
export function deleteBridgeProfileSuperstructure(id) {
  return request({
    url: `/bridge/usmBridgeProfileSuperstructure/${id}`,
    method: 'delete'
  })
}

// ==================== 下部结构 API ====================

// 获取下部结构详情
export function getBridgeProfileSubstructureDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileSubstructure/${bridgeId}`,
    method: 'get'
  })
}

// 新增下部结构
export function saveBridgeProfileSubstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSubstructure/save',
    method: 'post',
    data
  })
}

// 更新下部结构
export function updateBridgeProfileSubstructure(data) {
  return request({
    url: '/bridge/usmBridgeProfileSubstructure/update',
    method: 'post',
    data
  })
}

// 删除下部结构
export function deleteBridgeProfileSubstructure(id) {
  return request({
    url: `/bridge/usmBridgeProfileSubstructure/${id}`,
    method: 'delete'
  })
}

// ==================== 附属工程 API ====================

// 获取附属工程详情
export function getBridgeProfileAccessoryProjectDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfileAccessoryProject/${bridgeId}`,
    method: 'get'
  })
}

// 新增附属工程
export function saveBridgeProfileAccessoryProject(data) {
  return request({
    url: '/bridge/usmBridgeProfileAccessoryProject/save',
    method: 'post',
    data
  })
}

// 更新附属工程
export function updateBridgeProfileAccessoryProject(data) {
  return request({
    url: '/bridge/usmBridgeProfileAccessoryProject/update',
    method: 'post',
    data
  })
}

// 删除附属工程
export function deleteBridgeProfileAccessoryProject(id) {
  return request({
    url: `/bridge/usmBridgeProfileAccessoryProject/${id}`,
    method: 'delete'
  })
}

// ==================== 附挂管线 API ====================

// 获取附挂管线详情
export function getBridgeProfilePipelineDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeProfilePipeline/${bridgeId}`,
    method: 'get'
  })
}

// 新增附挂管线
export function saveBridgeProfilePipeline(data) {
  return request({
    url: '/bridge/usmBridgeProfilePipeline/save',
    method: 'post',
    data
  })
}

// 更新附挂管线
export function updateBridgeProfilePipeline(data) {
  return request({
    url: '/bridge/usmBridgeProfilePipeline/update',
    method: 'post',
    data
  })
}

// 删除附挂管线
export function deleteBridgeProfilePipeline(id) {
  return request({
    url: `/bridge/usmBridgeProfilePipeline/${id}`,
    method: 'delete'
  })
}

// ==================== 组成信息 API ====================

// 获取组成信息详情
export function getBridgeComponentInfoDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeComponentInfo/${bridgeId}`,
    method: 'get'
  })
}

// 新增组成信息
export function saveBridgeComponentInfo(data) {
  return request({
    url: '/bridge/usmBridgeComponentInfo/save',
    method: 'post',
    data
  })
}

// 更新组成信息
export function updateBridgeComponentInfo(data) {
  return request({
    url: '/bridge/usmBridgeComponentInfo/update',
    method: 'post',
    data
  })
}

// 删除组成信息
export function deleteBridgeComponentInfo(id) {
  return request({
    url: `/bridge/usmBridgeComponentInfo/${id}`,
    method: 'delete'
  })
}

// ==================== 附件资料 API ====================

// 获取附件资料详情
export function getBridgeAttachmentInfoDetail(bridgeId) {
  return request({
    url: `/bridge/usmBridgeAttachmentInfo/${bridgeId}`,
    method: 'get'
  })
}

// 新增附件资料
export function saveBridgeAttachmentInfo(data) {
  return request({
    url: '/bridge/usmBridgeAttachmentInfo/save',
    method: 'post',
    data
  })
}

// 更新附件资料
export function updateBridgeAttachmentInfo(data) {
  return request({
    url: '/bridge/usmBridgeAttachmentInfo/update',
    method: 'post',
    data
  })
}

// 删除附件资料
export function deleteBridgeAttachmentInfo(id) {
  return request({
    url: `/bridge/usmBridgeAttachmentInfo/${id}`,
    method: 'delete'
  })
}

// ==================== 养护单位 API ====================

// 获取养护单位列表
export function getMaintenanceEnterpriseList(data) {
  return request({
    url: '/bridge/usmMaintenanceEnterprise/list',
    method: 'post',
    data
  })
}

// ==================== 文件上传 API ====================

// 文件上传
export function uploadFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/common/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 桥梁监测报警阈值管理 API ====================

// 获取报警阈值分页列表
export function getAlarmThresholdPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmAlarmThreshold/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取报警阈值详情
export function getAlarmThresholdDetail(id) {
  return request({
    url: `/bridge/usmAlarmThreshold/${id}`,
    method: 'get'
  })
}

// 新增报警阈值
export function saveAlarmThreshold(data) {
  return request({
    url: '/bridge/usmAlarmThreshold/save',
    method: 'post',
    data
  })
}

// 更新报警阈值
export function updateAlarmThreshold(data) {
  return request({
    url: '/bridge/usmAlarmThreshold/update',
    method: 'post',
    data
  })
}

// 删除报警阈值
export function deleteAlarmThreshold(id) {
  return request({
    url: `/bridge/usmAlarmThreshold/${id}`,
    method: 'delete'
  })
}

// ==================== 桥梁报警信息管理 API ====================

// 获取桥梁报警列表
export function getBridgeAlarmList(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmMonitorAlarm/search/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁报警统计数据
export function getBridgeAlarmStatistics(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/statistics',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警等级统计数据
export function getBridgeAlarmLevelStatistics(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/level/statistics',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警详情
export function getBridgeAlarmDetail(id) {
  return request({
    url: `/bridge/usmMonitorAlarm/${id}`,
    method: 'get'
  })
}

// 确认桥梁报警
export function confirmBridgeAlarm(data) {
  return request({
    url: '/bridge/usmMonitorAlarm/alarm/confirm',
    method: 'post',
    data
  })
}

// 处置桥梁报警
export function handleBridgeAlarm(data) {
  return request({
    url: '/bridge/usmMonitorAlarm/handle',
    method: 'post',
    data
  })
}

// 获取桥梁报警监测曲线数据
export function getBridgeAlarmMonitorCurve(params) {
  return request({
    url: '/bridge/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警记录列表（根据设备ID）
export function getBridgeAlarmRecords(deviceId) {
  return request({
    url: `/bridge/usmMonitorAlarm/alarmRecord/${deviceId}`,
    method: 'get'
  })
}

// 获取桥梁报警状态记录列表
export function getBridgeAlarmStatusList(params) {
  return request({
    url: '/bridge/usmMonitorAlarmStatus/list',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警处置记录列表
export function getBridgeAlarmHandleList(alarmId) {
  return request({
    url: `/bridge/usmMonitorAlarm/alarm/handleList/${alarmId}`,
    method: 'get'
  })
}

// 新增桥梁报警处置记录
export function addBridgeAlarmHandle(data) {
  return request({
    url: '/bridge/usmMonitorAlarm/alarm/handle',
    method: 'post',
    data: data
  })
}

// 删除桥梁报警处置记录
export function deleteBridgeAlarmHandle(id) {
  return request({
    url: `/bridge/usmMonitorAlarmStatus/${id}`,
    method: 'delete'
  })
}

// 获取桥梁报警类型
export function getBridgeAlarmTypes() {
  return request({
    url: '/bridge/usmMonitorAlarm/getAlarmType',
    method: 'get'
  })
}

// ==================== 桥梁构件管理 API ====================

// 获取桥梁构件分页列表
export function getBridgeComponentPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmBridgeComponent/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁构件详情
export function getBridgeComponentDetail(id) {
  return request({
    url: `/bridge/usmBridgeComponent/${id}`,
    method: 'get'
  })
}

// 新增桥梁构件
export function saveBridgeComponent(data) {
  return request({
    url: '/bridge/usmBridgeComponent/save',
    method: 'post',
    data
  })
}

// 更新桥梁构件
export function updateBridgeComponent(data) {
  return request({
    url: '/bridge/usmBridgeComponent/update',
    method: 'post',
    data
  })
}

// 删除桥梁构件
export function deleteBridgeComponent(id) {
  return request({
    url: `/bridge/usmBridgeComponent/${id}`,
    method: 'delete'
  })
}

// ==================== 桥梁布点方案管理 API ====================

// 获取桥梁布点方案分页列表
export function getPointSchemePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmPointScheme/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁布点方案详情
export function getPointSchemeDetail(id) {
  return request({
    url: `/bridge/usmPointScheme/${id}`,
    method: 'get'
  })
}

// 新增桥梁布点方案
export function savePointScheme(data) {
  return request({
    url: '/bridge/usmPointScheme/save',
    method: 'post',
    data
  })
}

// 更新桥梁布点方案
export function updatePointScheme(data) {
  return request({
    url: '/bridge/usmPointScheme/update',
    method: 'post',
    data
  })
}

// 删除桥梁布点方案
export function deletePointScheme(id) {
  return request({
    url: `/bridge/usmPointScheme/${id}`,
    method: 'delete'
  })
}

// 获取设备类型
export function getDeviceType(type) {
  return request({
    url: '/bridge/usmMonitorDevice/getDeviceType',
    method: 'get',
    params: { type }
  })
}

// ==================== 设备分组管理 API ====================

// 获取设备分组分页列表
export function getDeviceGroupPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmDeviceGroup/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}
// 获取设备分组列表
export function getDeviceGroupList(params = {}) {
  return request({
    url: `/bridge/usmDeviceGroup/list`,
    method: 'post',
    data: params
  })
}

// 获取设备分组详情
export function getDeviceGroupDetail(id) {
  return request({
    url: `/bridge/usmDeviceGroup/${id}`,
    method: 'get'
  })
}

// 新增设备分组
export function saveDeviceGroup(data) {
  return request({
    url: '/bridge/usmDeviceGroup/save',
    method: 'post',
    data
  })
}

// 更新设备分组
export function updateDeviceGroup(data) {
  return request({
    url: '/bridge/usmDeviceGroup/update',
    method: 'post',
    data
  })
}

// 删除设备分组
export function deleteDeviceGroup(id) {
  return request({
    url: `/bridge/usmDeviceGroup/${id}`,
    method: 'delete'
  })
}

// 获取分组设备列表
export function getDeviceGroupDeviceList(groupId) {
  return request({
    url: '/bridge/usmDeviceGroupDevice/list',
    method: 'post',
    data: { groupId }
  })
}

// 新增设备到分组
export function saveDeviceToGroup(data) {
  return request({
    url: '/bridge/usmDeviceGroupDevice/save',
    method: 'post',
    data
  })
}

// 批量新增设备到分组
export function saveBatchDeviceToGroup(data) {
  return request({
    url: '/bridge/usmDeviceGroupDevice/saveBatch',
    method: 'post',
    data
  })
}

// 删除分组中的设备
export function deleteDeviceFromGroup(id) {
  return request({
    url: `/bridge/usmDeviceGroupDevice/${id}`,
    method: 'delete'
  })
}

// ==================== 分组类型常量 ====================

// 分组类型选项
export const GROUP_TYPE_OPTIONS = [
  { label: '同截面设备分组', value: '4020101' },
  { label: '静挠设备分组', value: '4020102' }
]

// ==================== 检测养护计划管理 API ====================

// 获取检测养护计划分页列表
export function getMaintainPlanPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmMaintainPlan/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取检测养护计划详情
export function getMaintainPlanDetail(id) {
  return request({
    url: `/bridge/usmMaintainPlan/${id}`,
    method: 'get'
  })
}

// 新增检测养护计划
export function saveMaintainPlan(data) {
  return request({
    url: '/bridge/usmMaintainPlan/save',
    method: 'post',
    data
  })
}

// 更新检测养护计划
export function updateMaintainPlan(data) {
  return request({
    url: '/bridge/usmMaintainPlan/update',
    method: 'post',
    data
  })
}

// 删除检测养护计划
export function deleteMaintainPlan(id) {
  return request({
    url: `/bridge/usmMaintainPlan/${id}`,
    method: 'delete'
  })
}

// ==================== 检测报告管理 API ====================

// 获取检测报告分页列表
export function getInspectReportPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmInspectReport/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取检测报告详情
export function getInspectReportDetail(id) {
  return request({
    url: `/bridge/usmInspectReport/${id}`,
    method: 'get'
  })
}

// 新增检测报告
export function saveInspectReport(data) {
  return request({
    url: '/bridge/usmInspectReport/save',
    method: 'post',
    data
  })
}

// 更新检测报告
export function updateInspectReport(data) {
  return request({
    url: '/bridge/usmInspectReport/update',
    method: 'post',
    data
  })
}

// 删除检测报告
export function deleteInspectReport(id) {
  return request({
    url: `/bridge/usmInspectReport/${id}`,
    method: 'delete'
  })
}

// ==================== 检测类型常量 ====================

// 检测类型选项
export const INSPECT_TYPE_OPTIONS = [
  { label: '定期检查', value: '1001' },
  { label: '特殊检查', value: '1002' },
  { label: '应急检查', value: '1003' },
  { label: '专项检查', value: '1004' }
]

// ==================== 维修养护数据管理 API ====================

// 获取维修养护记录分页列表
export function getMaintainRecordPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmMaintainRecord/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取维修养护记录详情
export function getMaintainRecordDetail(id) {
  return request({
    url: `/bridge/usmMaintainRecord/${id}`,
    method: 'get'
  })
}

// 新增维修养护记录
export function saveMaintainRecord(data) {
  return request({
    url: '/bridge/usmMaintainRecord/save',
    method: 'post',
    data
  })
}

// 更新维修养护记录
export function updateMaintainRecord(data) {
  return request({
    url: '/bridge/usmMaintainRecord/update',
    method: 'post',
    data
  })
}

// 删除维修养护记录
export function deleteMaintainRecord(id) {
  return request({
    url: `/bridge/usmMaintainRecord/${id}`,
    method: 'delete'
  })
}

// ==================== 病害数据管理 API ====================

// 获取病害记录分页列表
export function getDiseaseRecordPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmDiseaseRecord/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取病害记录详情
export function getDiseaseRecordDetail(id) {
  return request({
    url: `/bridge/usmDiseaseRecord/${id}`,
    method: 'get'
  })
}

// 新增病害记录
export function saveDiseaseRecord(data) {
  return request({
    url: '/bridge/usmDiseaseRecord/save',
    method: 'post',
    data
  })
}

// 更新病害记录
export function updateDiseaseRecord(data) {
  return request({
    url: '/bridge/usmDiseaseRecord/update',
    method: 'post',
    data
  })
}

// 删除病害记录
export function deleteDiseaseRecord(id) {
  return request({
    url: `/bridge/usmDiseaseRecord/${id}`,
    method: 'delete'
  })
}

// 获取桥梁构件列表（根据桥梁ID）
export function getBridgeComponentList(params = {}) {
  return request({
    url: '/bridge/usmBridgeComponent/list',
    method: 'post',
    data: params
  })
}

// ==================== 大屏统计接口 ====================

// 获取桥梁统计数据
export function getBridgeStatisticsScreen(type) {
  return request({
    url: '/bridge/api/v1/largeScreen/bridge/statistics',
    method: 'get',
    params: { type }
  })
}

// 获取桥梁监测统计数据
export function getBridgeMonitorStatistics(bridgeId) {
  return request({
    url: '/bridge/api/v1/largeScreen/monitor/statistics',
    method: 'get',
    params: bridgeId ? { bridgeId } : {}
  })
}

// 获取桥梁报警统计数据
export function getBridgeAlarmStatisticsScreen(params = {}) {
  return request({
    url: '/bridge/api/v1/largeScreen/alarm/statistics',
    method: 'post',
    params: params
  })
}
// 报警趋势（单桥页面）
export function getBridgeTrendsByBridgeId(params = {}) {
  const { bridgeId, dayIndex = 7 } = params
  return request({
    url: '/bridge/api/v1/largeScreen/alarm/trend',
    method: 'post',
    params: { bridgeId, dayIndex }
  })
}

// ==================== 病害数据管理常量 ====================

// 缺损类型选项 4004101:裂缝,4004102:变形,4004103:材料老化与损伤,4004104:连接部位失效,4004105:地基沉降,4004106:栏杆损坏,4004107:伸缩缝损坏,4004108:排水系统堵塞,4004199:其他
export const DEFECT_TYPE_OPTIONS = [
  { label: '裂缝', value: 4004101 },
  { label: '变形', value: 4004102 },
  { label: '材料老化与损伤', value: 4004103 },
  { label: '连接部位失效', value: 4004104 },
  { label: '地基沉降', value: 4004105 },
  { label: '栏杆损坏', value: 4004106 },
  { label: '伸缩缝损坏', value: 4004107 },
  { label: '排水系统堵塞', value: 4004108 },
  { label: '其他', value: 4004199 }
]

// 缺损类型映射
export const DEFECT_TYPE_MAP = {
  4004101: '裂缝',
  4004102: '变形',
  4004103: '材料老化与损伤',
  4004104: '连接部位失效',
  4004105: '地基沉降',
  4004106: '栏杆损坏',
  4004107: '伸缩缝损坏',
  4004108: '排水系统堵塞',
  4004199: '其他'
}

// 病害等级选项
export const DISEASE_LEVEL_OPTIONS = [
  { label: '轻微病害', value: 4003801 },
  { label: '轻度病害', value: 4003802 },
  { label: '中度病害', value: 4003803 },
  { label: '严重病害', value: 4003804 },
  { label: '危险病害', value: 4003805 }
]

// 病害等级映射
export const DISEASE_LEVEL_MAP = {
  4003801: '轻微病害',
  4003802: '轻度病害',
  4003803: '中度病害',
  4003804: '严重病害',
  4003805: '危险病害'
}

// 病害来源选项
export const DISEASE_SOURCE_OPTIONS = [
  { label: '例行检查', value: 4040301 },
  { label: '特殊检查', value: 4040302 },
  { label: '应急检查', value: 4040303 },
  { label: '监测报警', value: 4040304 },
  { label: '人工上报', value: 4040305 },
  { label: '其他', value: 4040399 }
]

// 病害来源映射
export const DISEASE_SOURCE_MAP = {
  4040301: '例行检查',
  4040302: '特殊检查',
  4040303: '应急检查',
  4040304: '监测报警',
  4040305: '人工上报',
  4040399: '其他'
}

// 处理类型选项
export const PROCESS_TYPE_OPTIONS = [
  { label: '日常养护', value: 4040401 },
  { label: '预防性养护', value: 4040402 },
  { label: '修复性养护', value: 4040403 },
  { label: '应急处置', value: 4040404 },
  { label: '专项维修', value: 4040405 },
  { label: '其他', value: 4040499 }
]

// 处理类型映射
export const PROCESS_TYPE_MAP = {
  4040401: '日常养护',
  4040402: '预防性养护',
  4040403: '修复性养护',
  4040404: '应急处置',
  4040405: '专项维修',
  4040499: '其他'
}

// 处理状态选项
export const PROCESS_STATUS_OPTIONS = [
  { label: '未处理', value: false },
  { label: '已处理', value: true }
]

// 处理状态映射
export const PROCESS_STATUS_MAP = {
  false: '未处理',
  true: '已处理'
}

// ==================== 环境数据监测与分析 API ====================

// 获取设备类型
export function getEnvironmentDeviceType(type = 1) {
  return request({
    url: '/bridge/usmMonitorDevice/getDeviceType',
    method: 'get',
    params: { type }
  })
}

// 获取设备类型统计
export function getEnvironmentDeviceTypeStatusStatistics(type = 1) {
  return request({
    url: '/bridge/usmMonitorDevice/getDeviceTypeStatistics',
    method: 'get',
    params: { type }
  })
}

// 获取设备状态统计
export function getEnvironmentDeviceStatusStatistics(type = 1) {
  return request({
    url: '/bridge/usmMonitorDevice/getDeviceStatusStatistics',
    method: 'get',
    params: { type }
  })
}

// 根据设备ID查询监测指标
export function getEnvironmentMonitorIndicators(deviceId) {
  return request({
    url: `/bridge/usmMonitorRecord/monitorIndicators/${deviceId}`,
    method: 'get'
  })
}

// 获取监测曲线数据
export function getEnvironmentMonitorCurve(data) {
  return request({
    url: '/bridge/usmMonitorRecord/monitorCurve',
    method: 'post',
    data
  })
}

// 获取监测历史记录分页
export function getEnvironmentMonitorRecordPage(pageNum, pageSize, data) {
  return request({
    url: `/bridge/usmMonitorRecord/monitorCurvePage/${pageNum}/${pageSize}`,
    method: 'post',
    data
  })
}

// 获取设备离线记录分页
export function getEnvironmentOfflineRecords(pageNum, pageSize, data) {
  return request({
    url: `/bridge/usmMonitorRecord/offlineRecords/${pageNum}/${pageSize}`,
    method: 'post',
    data
  })
}

// ==================== 交通荷载监测与分析 API ====================

// 获取交通荷载监测分页列表
export function getTrafficLoadMonitorPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmTrafficLoadMonitor/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取交通荷载监测详情
export function getTrafficLoadMonitorDetail(id) {
  return request({
    url: `/bridge/usmTrafficLoadMonitor/${id}`,
    method: 'get'
  })
}

// 获取监测设备列表（根据桥梁ID）
export function getMonitorDeviceListByBridge(bridgeId) {
  return request({
    url: '/bridge/usmMonitorDevice/list',
    method: 'post',
    data: { bridgeId }
  })
}

// ==================== 桥梁信息统计与分析 API ====================

// 获取桥梁统计数据
export function getBridgeStatistics(type) {
  return request({
    url: '/bridge/usmBridgeInfoAnalysis/bridge/statistics',
    method: 'get',
    params: { type }
  })
}

// 统计类型常量
export const STATISTICS_TYPE = {
  BRIDGE_TYPE: 1,     // 桥梁类型
  BRIDGE_AGE: 2,      // 桥梁年龄
  TECH_STATUS: 3,     // 技术状况
  MAINTAIN_TYPE: 4,   // 养护类型
  MAINTAIN_LEVEL: 5,  // 养护等级
  MAINTAIN_UNIT: 6    // 养护单位
}

// ==================== 桥梁报警信息统计与分析 API ====================

// 获取桥梁监测报警统计
export function getBridgeAlarmStatisticsAnalysis(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/statistics',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警处置、误报统计
export function getBridgeAlarmDisposalSituation(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/statistics/disposalSituation',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警趋势分析统计
export function getBridgeAlarmTrendStatistics(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/statistics',
    method: 'post',
    data: params
  })
}

// 获取桥梁报警等级统计
export function getBridgeAlarmLevelStatisticsAnalysis(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/level/statistics',
    method: 'post',
    data: params
  })
}

// 获取桥梁高发报警设备统计
export function getBridgeAlarmHighFrequencyDevices(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/device/high-frequency',
    method: 'post',
    data: params
  })
}

// 获取桥梁企业报警信息统计
export function getBridgeAlarmEnterpriseStatistics(params = {}) {
  return request({
    url: '/bridge/usmAlarmStatisticsAnalysis/enterprise/statistics',
    method: 'post',
    data: params
  })
}

// ==================== 安全评估统计与分析 API ====================

// 获取安全评估分数统计
export function getSecurityAssessmentScoreStatistics(params = {}) {
  return request({
    url: '/bridge/usmSecurityAssessmentAnalysis/score/statistics',
    method: 'get',
    params: params
  })
}

// 获取安全评估企业评分排名
export function getSecurityAssessmentEnterpriseStatistics(params = {}) {
  return request({
    url: '/bridge/usmSecurityAssessmentAnalysis/enterprise/statistics',
    method: 'get',
    params: params
  })
}

// 获取安全评估企业评分分布
export function getSecurityAssessmentDistributionStatistics(params = {}) {
  return request({
    url: '/bridge/usmSecurityAssessmentAnalysis/distribution/statistics',
    method: 'get',
    params: params
  })
}

// ==================== 超载数据统计与分析 API ====================

// 获取超载重量分布统计
export function getOverloadWeightStatistics(params = {}) {
  return request({
    url: '/bridge/usmOverloadAnalysis/weight/statistics',
    method: 'post',
    data: params
  })
}

// 获取超载次数分布统计
export function getOverloadCountStatistics(params = {}) {
  return request({
    url: '/bridge/usmOverloadAnalysis/count/statistics',
    method: 'post',
    data: params
  })
}

// 获取超载车牌来源分布统计
export function getOverloadSourceStatistics(params = {}) {
  return request({
    url: '/bridge/usmOverloadAnalysis/source/statistics',
    method: 'post',
    data: params
  })
}

// 获取超载车辆载重时间分布统计
export function getOverloadTimeStatistics(params = {}) {
  return request({
    url: '/bridge/usmOverloadAnalysis/time/statistics',
    method: 'post',
    data: params
  })
}

// ==================== 监测数据统计与分析 API ====================

// 获取设备总数统计
export function getMonitorDeviceTotalCount(params = {}) {
  return request({
    url: '/bridge/usmMonitorDeviceAnalysis/getTotalCount',
    method: 'post',
    data: params
  })
}

// 获取使用状态统计
export function getMonitorDeviceUseStatusStatistics(params = {}) {
  return request({
    url: '/bridge/usmMonitorDeviceAnalysis/useStatus/statistics',
    method: 'post',
    data: params
  })
}

// 获取监测状态统计
export function getMonitorDeviceOnlineStatusStatistics(params = {}) {
  return request({
    url: '/bridge/usmMonitorDeviceAnalysis/onlineStatus/statistics',
    method: 'post',
    data: params
  })
}

// 获取设备类型统计
export function getMonitorDeviceTypeStatistics(params = {}) {
  return request({
    url: '/bridge/usmMonitorDeviceAnalysis/deviceType/statistics',
    method: 'post',
    data: params
  })
}

// ==================== 养护记录上报 API ====================

// 养护记录上报
export function reportMaintainPlan(data) {
  return request({
    url: '/bridge/usmMaintainPlan/reporting',
    method: 'post',
    data
  })
}

// ==================== 安全评估报告管理 API ====================

// 获取安全评估报告分页列表
export function getSafetyAssessReportPage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmSafetyAssessReport/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取安全评估报告详情
export function getSafetyAssessReportDetail(id) {
  return request({
    url: `/bridge/usmSafetyAssessReport/${id}`,
    method: 'get'
  })
}

// 新增安全评估报告
export function saveSafetyAssessReport(data) {
  return request({
    url: '/bridge/usmSafetyAssessReport/save',
    method: 'post',
    data
  })
}

// 更新安全评估报告
export function updateSafetyAssessReport(data) {
  return request({
    url: '/bridge/usmSafetyAssessReport/update',
    method: 'post',
    data
  })
}

// 删除安全评估报告
export function deleteSafetyAssessReport(id) {
  return request({
    url: `/bridge/usmSafetyAssessReport/${id}`,
    method: 'delete'
  })
}

// ==================== 桥梁安全评分管理 API ====================

// 获取桥梁安全评分分页列表
export function getBridgeSafetyScorePage(pageNum, pageSize, params = {}) {
  return request({
    url: `/bridge/usmBridgeSafetyScore/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  })
}

// 获取桥梁安全评分详情
export function getBridgeSafetyScoreDetail(id) {
  return request({
    url: `/bridge/usmBridgeSafetyScore/${id}`,
    method: 'get'
  })
}

// ==================== 极值分析 API ====================

// 获取数据关联分析
export function getDataAssociationAnalysis(data) {
  return request({
    url: '/bridge/api/v1/dataAnalysis/association',
    method: 'post',
    data
  })
}

// ==================== 对比分析 API ====================

// 获取数据对比分析
export function getDataContrastAnalysis(data) {
  return request({
    url: '/bridge/api/v1/dataAnalysis/contrast',
    method: 'post',
    data
  })
}

// ==================== 多通道分析 API ====================

// 获取多通道分析数据
export function getMultichannelAnalysis(data) {
  return request({
    url: '/bridge/api/v1/dataAnalysis/multichannel',
    method: 'post',
    data
  })
}

// ==================== 挠度分析 API ====================

// 获取挠度分析数据
export function getDeflectionAnalysis(data) {
  return request({
    url: '/bridge/api/v1/dataAnalysis/deflection',
    method: 'post',
    data
  })
}

// ==================== 多设备频谱分析 API ====================

// 获取多设备频谱分析数据
export function getMultiDeviceAnalysis(data) {
  return request({
    url: '/bridge/api/v1/dataAnalysis/multidevice',
    method: 'post',
    data
  })
}

// ==================== 多时段频谱分析 API ====================

// 获取多时段频谱分析数据
export function getMultiPeriodAnalysis(data) {
  return request({
    url: '/bridge/api/v1/dataAnalysis/multiperiod',
    method: 'post',
    data
  })
}

// ==================== 滤波分析 API ====================

// 获取滤波分析数据
export function getFilterAnalysis(data) {
  return request({
    url: '/bridge/api/v1/dataAnalysis/peak',
    method: 'post',
    data
  })
}

// ==================== 大屏安全评分接口 ====================

// 获取桥梁安全评分统计数据
export function getBridgeSafetyScoreStatistics() {
  return request({
    url: '/bridge/api/v1/largeScreen/score/statistics',
    method: 'get'
  })
}

// ==================== 大屏病害统计接口 ====================

// 获取桥梁病害统计数据
export function getBridgeDiseaseStatistics(params = {}) {
  const { pageNum = 1, pageSize = 9999, dayIndex = 7, bridgeId } = params
  return request({
    url: '/bridge/api/v1/largeScreen/disease/statistics',
    method: 'post',
    params: {
      pageNum,
      pageSize,
      dayIndex,
      bridgeId
    }
  })
}

